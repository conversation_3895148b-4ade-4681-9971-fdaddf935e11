{"name": "zonacero_portal", "version": "0.1.0", "private": true, "dependencies": {"axios": "^1.12.2", "bcryptjs": "^2.4.3", "boxicons": "^2.1.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "googleapis": "^164.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "react": "^19.2.0", "react-dom": "^19.2.0", "react-router-dom": "^7.9.4", "react-scripts": "5.0.1", "styled-components": "^6.1.19", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject", "server": "node backend/server.js", "run-schema": "node backend/run-schema.js", "run-migration": "node backend/run-migration.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}